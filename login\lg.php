<?php
session_start();
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// Preflight
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') exit(0);

// Debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

function logError($message) {
    error_log(date('[Y-m-d H:i:s] ') . $message . "\n", 3, 'login_errors.log');
}

// Cookie check
if (isset($_COOKIE['verified']) && $_COOKIE['verified'] === 'true') {
    $user_data = $_SESSION['user_data'] ?? [];
    echo json_encode([
        "status" => "verified",
        "redirect" => "../Data/index.html",
        "user_data" => $user_data // Include user_data for client-side
    ]);
    exit();
}

// DB
$host = "localhost";
$user = "root";
$pass = "";
$dbname = "leadbase";

try {
    $conn = new mysqli($host, $user, $pass, $dbname);
    if ($conn->connect_error) throw new Exception("Connection failed: " . $conn->connect_error);
} catch (Exception $e) {
    logError("DB Error: " . $e->getMessage());
   http_response_code(500);
    echo json_encode(["error" => "Database connection failed"]);
    exit();
}

require 'PHPMailer-master/src/PHPMailer.php';
require 'PHPMailer-master/src/SMTP.php';
require 'PHPMailer-master/src/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

function sendEmailSMTP($to, $subject, $body) {
    $mail = new PHPMailer(true);
    try {
        $mail->isSMTP();
        $mail->Host = 'smtp.hostinger.com';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>';
        $mail->Password = '@Aakash14471';
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
        $mail->Port = 465;

        $mail->setFrom('<EMAIL>', 'LeadBaseAI');
        $mail->addAddress($to);
        $mail->Subject = $subject;
        $mail->Body = $body;

        $mail->send();
        return true;
    } catch (Exception $e) {
        logError("SMTP error: " . $mail->ErrorInfo);
        return false;
    }
}

// Get input
$input = file_get_contents("php://input");
if (!$input) {
    logError("No input received");
    http_response_code(400);
    echo json_encode(["error" => "No data received"]);
    exit();
}

$data = json_decode($input, true);
if (!$data) {
    logError("Invalid JSON: " . $input);
    http_response_code(400);
    echo json_encode(["error" => "Invalid JSON"]);
    exit();
}

// OTP VERIFY FIRST
if (isset($data['otp'])) {
    $otp = trim($data['otp']);
    $sessionOtp = $_SESSION['code'] ?? '';
    $userData = $_SESSION['user_data'] ?? [];

    logError("OTP Verify - Input: $otp | Expected: $sessionOtp");

    if (empty($sessionOtp) || empty($userData)) {
        echo json_encode(["error" => "Session expired. Please try again."]);
        exit();
    }

    if ($otp === $sessionOtp) {
        // Clear OTP
        unset($_SESSION['code']);

        // Set Cookie
        setcookie("verified", "true", time() + (10 * 365 * 24 * 60 * 60), "/");

        // Insert user if not exists
        $stmt = $conn->prepare("SELECT * FROM users WHERE Email = ?");
        $stmt->bind_param("s", $userData['email']);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            $insert = $conn->prepare("INSERT INTO users (Name, Email, PhoneNumber, Dailylimit, ExtraLimit, Affiliate, Plan) VALUES (?, ?, ?, 100, 0, 0, 'Free')");
            $insert->bind_param("sss", $userData['name'], $userData['email'], $userData['phone']);
            $insert->execute();
            logError("User created: " . $userData['email']);
        } else {
            logError("User exists: " . $userData['email']);
        }

        echo json_encode([
            "status" => "verified",
            "redirect" => "../Data/index.html",
            "user_data" => $userData // Return user_data for client-side
        ]);
    } else {
        echo json_encode(["error" => "Invalid OTP"]);
    }
    exit();
}

// MAIN LOGIN/OTP STEP
$email = trim($data['email'] ?? '');
$name = trim($data['name'] ?? '');
$phone = trim($data['phone'] ?? '');

// Validation
if (!$email || !$name || !$phone) {
    echo json_encode(["error" => "All fields are required"]);
    exit();
}
if (!preg_match('/^[a-zA-Z\s]{2,50}$/', $name)) {
    echo json_encode(["error" => "Name must be 2–50 letters"]);
    exit();
}
if (!filter_var($email, FILTER_VALIDATE_EMAIL) || !preg_match('/@gmail\.com$/', $email)) {
    echo json_encode(["error" => "Only Gmail addresses allowed"]);
    exit();
}
if (!preg_match('/^\+?\d{10,15}$/', $phone)) {
    echo json_encode(["error" => "Invalid phone format"]);
    exit();
}

// Save user data in session
$_SESSION['user_data'] = [
    "email" => $email,
    "name" => $name,
    "phone" => $phone
];

// Generate and send OTP
$code = sprintf("%06d", random_int(100000, 999999));
$_SESSION['code'] = $code;

logError("OTP for $email: $code");

$subject = "LeadBaseAI - Your OTP Code";
$message = "Hi $name,\n\nYour OTP is: $code\n\nExpires in 10 minutes.\n\nRegards,\nLeadBaseAI";

if (sendEmailSMTP($email, $subject, $message)) {
    echo json_encode(["status" => "otp_sent", "message" => "OTP sent to your email"]);
} else {
    echo json_encode(["error" => "OTP email failed. Try again."]);
}
?>