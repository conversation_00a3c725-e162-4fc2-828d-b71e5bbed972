<?php
// Enable error reporting and logging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set up local logging
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/debug.log');

// Custom logging function
function logDebug($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] DEBUG: $message" . PHP_EOL;
    file_put_contents(__DIR__ . '/debug.log', $logMessage, FILE_APPEND | LOCK_EX);
}

// Custom error logging function
function logError($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] ERROR: $message" . PHP_EOL;
    file_put_contents(__DIR__ . '/debug.log', $logMessage, FILE_APPEND | LOCK_EX);
}

logDebug("Script started. Request: " . $_SERVER['REQUEST_URI']);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Database configurations
$leads_config = [
    'host' => 'localhost',
    'dbname' => 'leads',
    'username' => 'root',
    'password' => ''
];

$users_config = [
    'host' => 'localhost',
    'dbname' => 'leadbase', // ⚠️ UPDATE THIS
    'username' => 'root',
    'password' => ''
];

// Initialize connections
$pdo_leads = null;
$pdo_users = null;

try {
    logDebug("Attempting leads database connection...");
    $pdo_leads = new PDO(
        "mysql:host={$leads_config['host']};dbname={$leads_config['dbname']};charset=utf8", 
        $leads_config['username'], 
        $leads_config['password']
    );
    $pdo_leads->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    logDebug("Leads database connection successful");
} catch (PDOException $e) {
    logError("Leads database connection failed: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Leads DB Error: ' . $e->getMessage()]);
    exit;
}

try {
    logDebug("Attempting users database connection...");
    $pdo_users = new PDO(
        "mysql:host={$users_config['host']};dbname={$users_config['dbname']};charset=utf8", 
        $users_config['username'], 
        $users_config['password']
    );
    $pdo_users->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    logDebug("Users database connection successful");
} catch (PDOException $e) {
    logError("Users database connection failed: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Users DB Error: ' . $e->getMessage()]);
    exit;
}

// Get action type
$action = $_GET['action'] ?? '';
logDebug("Action received: '$action'");

// === 1. Fetch limits for a given email (uses users database) ===
if ($action === 'get_limits') {
    logDebug("Processing get_limits action");
    
    $email = $_GET['email'] ?? '';
    logDebug("Email parameter: '$email'");
    
    if (empty($email)) {
        logError("No email provided for get_limits");
        echo json_encode(['success' => false, 'error' => 'No email provided']);
        exit;
    }

    try {
        logDebug("Preparing SQL query for get_limits on users database");
        
        // First, let's check if the user exists
        $checkStmt = $pdo_users->prepare("SELECT COUNT(*) as count FROM users WHERE Email = ?");
        $checkStmt->execute([$email]);
        $userExists = $checkStmt->fetch(PDO::FETCH_ASSOC);
        logDebug("User exists check: " . json_encode($userExists));
        
        // Now get the limits
        $stmt = $pdo_users->prepare("SELECT Dailylimit, ExtraLimit FROM users WHERE Email = ?");
        $stmt->execute([$email]);
        $limits = $stmt->fetch(PDO::FETCH_ASSOC);
        
        logDebug("Raw database result: " . json_encode($limits));

        if ($limits) {
            // Convert to consistent naming for JavaScript
            $response = [
                'success' => true, 
                'limits' => [
                    'dailyLimit' => $limits['Dailylimit'],
                    'extraLimit' => $limits['ExtraLimit']
                ]
            ];
            logDebug("Sending successful response: " . json_encode($response));
            echo json_encode($response);
        } else {
            logError("User not found for email: $email");
            echo json_encode(['success' => false, 'error' => 'User not found']);
        }
        
    } catch (PDOException $e) {
        logError("Database error in get_limits: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
    } catch (Exception $e) {
        logError("General error in get_limits: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'Error: ' . $e->getMessage()]);
    }
    
    logDebug("get_limits action completed");
    exit;
}

// === 2. Fetch contacts (uses leads database) ===
if ($action !== 'get_contacts') {
    logError("Invalid action received: '$action'");
    echo json_encode(['success' => false, 'error' => 'Invalid action']);
    exit;
}

logDebug("Processing get_contacts action");

$table = $_GET['country'] ?? '';
$special = $_GET['special_filter'] ?? '';

logDebug("Country/table: '$table', Special filter: '$special'");

if (empty($table)) {
    logError("No table selected for get_contacts");
    echo json_encode(['success' => false, 'error' => 'No table selected']);
    exit;
}

// ⚠️ Prevent SQL Injection (sanitize table name)
if (!preg_match('/^[a-zA-Z0-9_]+$/', $table)) {
    logError("Invalid table name: '$table'");
    echo json_encode(['success' => false, 'error' => 'Invalid table name']);
    exit;
}

// Build WHERE conditions
$where = [];
if ($special === 'have_email') {
    $where[] = "email IS NOT NULL AND email != ''";
} elseif ($special === 'have_phone') {
    $where[] = "phone IS NOT NULL AND phone != ''";
} elseif ($special === 'have_both') {
    $where[] = "email IS NOT NULL AND email != '' AND phone IS NOT NULL AND phone != ''";
}
$whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

// Final SQL
$sql = "SELECT name, phone, email, bio, fb_url 
        FROM `$table`
        $whereClause
        ORDER BY id DESC";

logDebug("Final SQL query: $sql");

try {
    $stmt = $pdo_leads->query($sql);
    $contacts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    logDebug("Contacts fetched successfully. Count: " . count($contacts));
    echo json_encode(['success' => true, 'data' => $contacts]);
} catch (PDOException $e) {
    logError("Query error in get_contacts: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Query Error: ' . $e->getMessage()]);
}

logDebug("Script execution completed");
?>