<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LeadFlow - Professional Contact Management</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <link rel="apple-touch-icon" sizes="180x180" href="../icons/180-180.png">
    <link rel="icon" type="image/png" sizes="32x32" href="../icons/32-32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="../icons/16-16.png">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --accent-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --dark-gradient: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            --glass-bg: rgba(255, 255, 255, 0.95);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.08);
            --shadow-medium: 0 12px 40px rgba(0, 0, 0, 0.12);
            --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.16);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1e293b;
            line-height: 1.6;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23667eea" stop-opacity="0.05"/><stop offset="100%" stop-color="%23764ba2" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="300" fill="url(%23a)"/><circle cx="800" cy="800" r="400" fill="url(%23a)"/></svg>') no-repeat center center fixed;
            background-size: cover;
            z-index: -1;
            pointer-events: none;
        }

        /* Header */
        .header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--shadow-soft);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo {
            width: 40px;
            height: 40px;
            background: var(--primary-gradient);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-medium);
            transition: var(--transition);
        }

        .logo:hover {
            transform: translateY(-2px) scale(1.05);
        }

        .brand-info h1 {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 2px;
        }

        .brand-info p {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        #streakBox {
            margin-left: auto;
            margin-right: 20px;
            padding: 12px 24px;
            background: var(--accent-gradient);
            color: white;
            border-radius: 16px;
            font-weight: 600;
            font-size: 14px;
            box-shadow: var(--shadow-medium);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: var(--transition);
        }

        #streakBox:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-strong);
        }

        .user-avatar {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 16px;
            box-shadow: var(--shadow-medium);
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: var(--transition);
            cursor: pointer;
        }

        .user-avatar:hover {
            transform: translateY(-2px) scale(1.05);
        }

        /* Main Container */
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2.5rem;
        }

        .breadcrumb {
            margin-bottom: 2.5rem;
            color: #64748b;
            font-size: 14px;
            font-weight: 500;
        }

        .breadcrumb a {
            color: #3b82f6;
            text-decoration: none;
            transition: var(--transition);
        }

        .breadcrumb a:hover {
            color: #2563eb;
        }

        .page-header {
            margin-bottom: 2.5rem;
            text-align: center;
        }

        .page-title {
            font-size: 36px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.75rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 18px;
            font-weight: 500;
        }

        /* Controls Section */
        .controls {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-medium);
            border: 1px solid var(--glass-border);
            transition: var(--transition);
        }

        .controls:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-strong);
        }

        .controls-row {
            display: flex;
            gap: 1.5rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 320px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 1rem 1.25rem 1rem 3rem;
            border: 2px solid transparent;
            border-radius: var(--border-radius);
            font-size: 15px;
            transition: var(--transition);
            background: rgba(248, 250, 252, 0.8);
            backdrop-filter: blur(10px);
            font-weight: 500;
        }

        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            background: white;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
            transform: translateY(-1px);
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
            transition: var(--transition);
        }

        .search-input:focus + .search-icon {
            color: #3b82f6;
        }

        .filter-group {
            display: flex;
            gap: 1rem;
        }

        .filter-select {
            padding: 1rem 1.25rem;
            border: 2px solid transparent;
            border-radius: var(--border-radius);
            font-size: 15px;
            background: rgba(248, 250, 252, 0.8);
            backdrop-filter: blur(10px);
            min-width: 160px;
            transition: var(--transition);
            font-weight: 500;
            cursor: pointer;
        }

        .filter-select:focus {
            outline: none;
            border-color: #3b82f6;
            background: white;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
            transform: translateY(-1px);
        }

        /* Action Bar */
        .action-bar {
            background: var(--dark-gradient);
            color: white;
            padding: 1.25rem 2rem;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            display: none;
            align-items: center;
            justify-content: space-between;
            animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-strong);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .action-bar.show {
            display: flex;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .selected-info {
            font-weight: 600;
            font-size: 15px;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: var(--shadow-medium);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-strong);
        }

        .btn-secondary {
            background: rgba(100, 116, 139, 0.9);
            color: white;
            box-shadow: var(--shadow-medium);
        }

        .btn-secondary:hover {
            background: rgba(71, 85, 105, 0.9);
            transform: translateY(-2px);
            box-shadow: var(--shadow-strong);
        }

        /* Table Container */
        .table-container {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-strong);
            border: 1px solid var(--glass-border);
            transition: var(--transition);
        }

        .table-container:hover {
            transform: translateY(-4px);
        }

        .table-header {
            padding: 2rem;
            border-bottom: 1px solid rgba(226, 232, 240, 0.5);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(248, 250, 252, 0.5);
        }

        .table-title {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
        }

        .results-info {
            color: #64748b;
            font-size: 15px;
            font-weight: 600;
            padding: 0.5rem 1rem;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        /* Table styling for visibility and responsiveness */
        #contactTable {
            font-size: 12px;
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        #contactTable th,
        #contactTable td {
            border: 1px solid rgba(208, 208, 208, 0.5);
            padding: 1rem;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        #contactTable th {
            background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
            font-weight: 700;
            position: sticky;
            top: 0;
            z-index: 10;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: #475569;
            backdrop-filter: blur(10px);
            transition: var(--transition);
        }

        #contactTable th:hover {
            background: linear-gradient(135deg, rgba(226, 232, 240, 0.8) 0%, rgba(203, 213, 225, 0.8) 100%);
        }

        #contactTable tr:nth-child(even) {
            background: rgba(248, 250, 252, 0.3);
        }

        #contactTable tr.selected {
            background: rgba(219, 234, 254, 0.8);
            backdrop-filter: blur(10px);
        }

        #contactTable tbody tr:hover {
            background: rgba(240, 249, 255, 0.8);
            cursor: pointer;
            transform: scale(1.002);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .table-container {
            overflow-x: auto;
            max-width: 100%;
        }

        /* Responsive column widths */
        #contactTable th:nth-child(1) { width: 5%; }
        #contactTable th:nth-child(2) { width: 20%; }
        #contactTable th:nth-child(3) { width: 14%; }
        #contactTable th:nth-child(4) { width: 14%; }
        #contactTable th:nth-child(5) { width: 40%; }
        #contactTable th:nth-child(6) { width: 7%; }

        .checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 6px;
            cursor: pointer;
            transition: var(--transition);
            appearance: none;
            position: relative;
        }

        .checkbox:checked {
            background: var(--primary-gradient);
            border-color: transparent;
        }

        .checkbox:checked::after {
            content: '✓';
            position: absolute;
            color: white;
            font-size: 12px;
            font-weight: bold;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .contact-name {
            font-weight: 600;
            color: #1e293b;
        }

        .contact-phone {
            color: #64748b;
            font-family: 'SF Mono', Monaco, monospace;
            font-weight: 500;
        }

        .contact-email {
            color: #64748b;
            font-weight: 500;
        }

        .bio-text {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #64748b;
            font-weight: 400;
        }

        .facebook-link {
            color: #3b82f6;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
        }

        .facebook-link:hover {
            color: #2563eb;
            background: rgba(59, 130, 246, 0.1);
        }

        .sort-icon {
            margin-left: 0.5rem;
            opacity: 0.4;
            transition: var(--transition);
        }

        th.sortable:hover .sort-icon {
            opacity: 1;
        }

        /* Pagination */
        .pagination {
            padding: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid rgba(226, 232, 240, 0.5);
            background: linear-gradient(135deg, rgba(248, 250, 252, 0.5) 0%, rgba(241, 245, 249, 0.5) 100%);
        }

        .pagination-info {
            color: #64748b;
            font-size: 14px;
            font-weight: 600;
        }

        .pagination-controls {
            display: flex;
            gap: 0.75rem;
            align-items: center;
        }

        .page-btn {
            padding: 0.75rem 1rem;
            border: 2px solid transparent;
            background: white;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: var(--transition);
            box-shadow: var(--shadow-soft);
        }

        .page-btn:hover {
            background: #f3f4f6;
            border-color: #3b82f6;
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }

        .page-btn.active {
            background: var(--primary-gradient);
            color: white;
            border-color: transparent;
        }

        .page-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            transform: none;
        }

        /* Daily Limit Display */
        div[style*="margin:10px 0"] {
            margin: 1.5rem 0 !important;
            font-size: 15px !important;
            font-weight: 600 !important;
            padding: 1rem 1.5rem !important;
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px) !important;
            border-radius: var(--border-radius) !important;
            border: 1px solid var(--glass-border) !important;
            box-shadow: var(--shadow-soft) !important;
        }

        /* Profile Menu Enhancement */
        .profile-menu {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px) !important;
            border: 1px solid var(--glass-border) !important;
            border-radius: var(--border-radius) !important;
            box-shadow: var(--shadow-strong) !important;
            padding: 1.5rem !important;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 1.5rem;
            }

            .header {
                padding: 1rem 1.5rem;
            }

            .controls-row {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                min-width: unset;
            }

            .filter-group {
                justify-content: stretch;
            }

            .filter-select {
                flex: 1;
            }

            .page-title {
                font-size: 28px;
            }

            #contactTable {
                font-size: 11px;
            }

            #contactTable th,
            #contactTable td {
                padding: 0.75rem;
            }

            #contactTable th:nth-child(1) { width: 8%; }
            #contactTable th:nth-child(2) { width: 25%; }
            #contactTable th:nth-child(3) { width: 20%; }
            #contactTable th:nth-child(4) { width: 25%; }
            #contactTable th:nth-child(5) { width: 20%; }
            #contactTable th:nth-child(6) { width: 12%; }
        }

        @media (max-width: 480px) {
            #contactTable {
                font-size: 10px;
            }

            #contactTable th,
            #contactTable td {
                padding: 0.5rem;
            }

            .controls {
                padding: 1.5rem;
            }

            .table-header {
                padding: 1.5rem;
            }

            .pagination {
                padding: 1.5rem;
            }
        }

        /* Subtle animations for enhanced UX */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .container > * {
            animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            animation-fill-mode: both;
        }

        .container > *:nth-child(1) { animation-delay: 0.1s; }
        .container > *:nth-child(2) { animation-delay: 0.2s; }
        .container > *:nth-child(3) { animation-delay: 0.3s; }
        .container > *:nth-child(4) { animation-delay: 0.4s; }
        .container > *:nth-child(5) { animation-delay: 0.5s; }
    </style>
</head>

<body>
    <!-- Header -->
    <header class="header">
        <div class="logo-section">
            <div class="logo">LF</div>
            <div class="brand-info">
                <h1>LeadFlow</h1>
                <p>Professional Contact Management</p>
            </div>
        </div>
        <div class="user-section">
            <div class="user-avatar" id="userAvatar">--</div>
            <div class="profile-menu" id="profileMenu"
                style="display:none;position:absolute;top:60px;right:20px;background:white;border:1px solid #ccc;padding:10px;border-radius:6px;z-index:100;">
            </div>
            <div id="streakBox"
                style="margin-left:auto;margin-right:20px;padding:10px 20px;background:#4caf50;color:white;border-radius:12px;font-weight:bold;">
                🔥 Streak: 0
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        <!-- Breadcrumb -->
        <div class="breadcrumb">
            <a href="#">Dashboard</a> > Contact Database
        </div>
        <div style="margin:10px 0;font-size:14px;">
            Daily Limit: <span id="dailyLimit">...</span> | Extra Limit: <span id="extraLimit">...</span>
        </div>

        <!-- Page Header -->
        <div class="page-header">
            <h2 class="page-title">Contact Database</h2>
            <p class="page-subtitle">Manage and organize your professional contacts</p>
        </div>

        <!-- Controls -->
        <div class="controls">
            <div class="controls-row">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="Search contacts..." id="searchInput">
                    <svg class="search-icon" width="18" height="18" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                </div>
                <div class="filter-group">
                    <select class="filter-select" id="countryFilter">
                        <option value="" disabled selected>All Countries</option>
                        <option value="usa" selected>USA</option>
                        <option value="canada">Canada</option>
                        <option value="uk">United Kingdom</option>
                        <option value="india">India</option>
                        <option value="australia">Australia</option>
                        <option value="uae">UAE</option>
                    </select>
                    <select class="filter-select" id="specialFilter">
                        <option value="" disabled selected>Advance Filters</option>
                        <option value="none">None</option>
                        <option value="have_email">Have Email</option>
                        <option value="have_phone">Have Phone</option>
                        <option value="have_both">Have Both</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Action Bar -->
        <div class="action-bar" id="actionBar">
            <div class="selected-info">
                <span id="selectedCount">0</span> contacts selected
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" id="downloadBtn">Download</button>
                <button class="btn btn-secondary" id="saveBtn">Save</button>
            </div>
        </div>

        <!-- Table Container -->
        <div class="table-container">
            <div class="table-header">
                <h3 class="table-title">Available Data</h3>
                <div class="results-info">Total: <span id="totalResults">1,280</span> contacts</div>
            </div>

            <table id="contactTable">
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <input type="checkbox" class="checkbox" id="selectAll">
                        </th>
                        <th class="sortable">
                            Name
                            <span class="sort-icon">↕</span>
                        </th>
                        <th class="sortable">
                            Phone
                            <span class="sort-icon">↕</span>
                        </th>
                        <th class="sortable">
                            Email
                            <span class="sort-icon">↕</span>
                        </th>
                        <th>Bio</th>
                        <th>Facebook</th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="pagination">
                <div class="pagination-info" id="paginationInfo">
                    Showing 1-10 of 1,280 contacts
                </div>
                <div class="pagination-controls">
                    <button class="page-btn" id="prevBtn" disabled>← Prev</button>
                    <span id="pageNumbers"></span>
                    <button class="page-btn" id="nextBtn">Next →</button>
                </div>
            </div>
        </div>
    </div>
    <script src="scripts.js"></script>
</body>

</html>