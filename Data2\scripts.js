let allData = [];
let filteredData = [];
let currentPage = 1;
const rowsPerPage = 20;

// === FETCH CONTACTS ===
async function fetchContacts() {
    const country = document.getElementById("countryFilter").value;
    const filter = document.getElementById("specialFilter").value;

    const url = new URL("http://localhost/leadbase/Data/data.php");
    url.searchParams.append("action", "get_contacts");
    if (country) url.searchParams.append("country", country);
    if (filter && filter !== "none") {
        url.searchParams.append("special_filter", filter);
    }

    try {
        const res = await fetch(url);
        const json = await res.json();

        if (!json.success) throw new Error("Fetch failed");

        allData = json.data;
        filteredData = [...allData];
        currentPage = 1;
        renderTable();
        updateTotalResults();
    } catch (err) {
        alert("Error fetching contacts: " + err.message);
        console.error(err);
    }
}

// === FETCH LIMITS USING EMAIL FROM LOCALSTORAGE ===
async function fetchUserLimits() {
    // Get user data from localStorage
    const meta = JSON.parse(localStorage.getItem("user_data") || "{}");
    const email = meta.email;
    const name = meta.name || "--";
    
    // Update avatar with first two letters of name
    document.getElementById("userAvatar").textContent = name.slice(0, 2).toUpperCase();
    
    if (!email) {
        console.warn("No email found in localStorage");
        return;
    }

    try {
        const res = await fetch(`http://localhost/leadbase/Data/data.php?action=get_limits&email=${encodeURIComponent(email)}`);
        const json = await res.json();
        if (json.success && json.limits) {
            document.getElementById("dailyLimit").textContent = json.limits.dailyLimit;
            document.getElementById("extraLimit").textContent = json.limits.extraLimit;
        } else {
            console.warn("Limit fetch failed:", json.error || "Unknown error");
        }
    } catch (err) {
        console.error("Failed to fetch limits:", err);
    }
}

// === RENDER TABLE ===
function renderTable() {
    const tbody = document.getElementById("tableBody");
    tbody.innerHTML = "";

    const start = (currentPage - 1) * rowsPerPage;
    const end = start + rowsPerPage;
    const pageData = filteredData.slice(start, end);

    pageData.forEach(row => {
        const tr = document.createElement("tr");
        tr.innerHTML = `
            <td><input type="checkbox" class="checkbox row-checkbox"></td>
            <td>${row.name || 'N/A'}</td>
            <td>${row.phone || 'N/A'}</td>
            <td>${row.email || 'N/A'}</td>
            <td>${row.bio || 'N/A'}</td>
            <td>${row.fb_url ? '<a href="' + row.fb_url + '" class="facebook-link" target="_blank">Link</a>' : 'N/A'}</td>
        `;
        tr.addEventListener("click", function (e) {
            if (!e.target.classList.contains("row-checkbox") && e.target.tagName !== 'A') {
                const cb = tr.querySelector(".row-checkbox");
                cb.checked = !cb.checked;
                tr.classList.toggle("selected", cb.checked);
                updateActionBar();
            }
        });
        tbody.appendChild(tr);
    });

    attachRowEvents();
    renderPagination();
}

// === PAGINATION ===
function renderPagination() {
    const totalPages = Math.ceil(filteredData.length / rowsPerPage);
    const start = (currentPage - 1) * rowsPerPage + 1;
    const end = Math.min(currentPage * rowsPerPage, filteredData.length);

    document.getElementById("paginationInfo").textContent =
        `Showing ${start}-${end} of ${filteredData.length} contacts`;

    const pageNumbers = document.getElementById("pageNumbers");
    pageNumbers.innerHTML = "";

    const prevBtn = document.getElementById("prevBtn");
    prevBtn.disabled = currentPage === 1;
    prevBtn.onclick = () => {
        if (currentPage > 1) {
            currentPage--;
            renderTable();
        }
    };

    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
        addPageButton(1);
        if (startPage > 2) {
            pageNumbers.appendChild(document.createTextNode("..."));
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        addPageButton(i);
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            pageNumbers.appendChild(document.createTextNode("..."));
        }
        addPageButton(totalPages);
    }

    const nextBtn = document.getElementById("nextBtn");
    nextBtn.disabled = currentPage === totalPages;
    nextBtn.onclick = () => {
        if (currentPage < totalPages) {
            currentPage++;
            renderTable();
        }
    };
}

function addPageButton(pageNum) {
    const btn = document.createElement("button");
    btn.className = "page-btn" + (pageNum === currentPage ? " active" : "");
    btn.textContent = pageNum;
    btn.onclick = () => {
        currentPage = pageNum;
        renderTable();
    };
    document.getElementById("pageNumbers").appendChild(btn);
}

// === ROW EVENT BINDING ===
function attachRowEvents() {
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function () {
            this.closest('tr').classList.toggle('selected', this.checked);
            updateActionBar();
        });
    });

    document.getElementById('selectAll').addEventListener('change', function () {
        rowCheckboxes.forEach(cb => {
            cb.checked = this.checked;
            cb.closest('tr').classList.toggle('selected', this.checked);
        });
        updateActionBar();
    });
}

// === ACTION BAR ===
function updateActionBar() {
    const count = document.querySelectorAll('.row-checkbox:checked').length;
    const bar = document.getElementById("actionBar");
    document.getElementById("selectedCount").textContent = count;
    bar.classList.toggle("show", count > 0);
}

// === SEARCH ===
function performSearch() {
    const term = document.getElementById("searchInput").value.toLowerCase();

    if (term === "") {
        filteredData = [...allData];
    } else {
        filteredData = allData.filter(row =>
            (row.name && row.name.toLowerCase().includes(term)) ||
            (row.phone && row.phone.toLowerCase().includes(term)) ||
            (row.email && row.email.toLowerCase().includes(term)) ||
            (row.bio && row.bio.toLowerCase().includes(term))
        );
    }

    currentPage = 1;
    renderTable();
    updateTotalResults();
}

// === TOTAL RESULTS ===
function updateTotalResults() {
    document.getElementById("totalResults").textContent = filteredData.length;
}

// === PROFILE INIT ===
document.addEventListener("DOMContentLoaded", () => {
    const meta = JSON.parse(localStorage.getItem("user_data") || "{}");
    const name = meta.name || "--";
    const streak = meta.streak ?? 0;

    // Display first two letters of name in avatar (already done in fetchUserLimits, but keeping here for safety)
    document.getElementById("userAvatar").textContent = name.slice(0, 2).toUpperCase();
    document.getElementById("streakBox").textContent = `🔥 Streak: ${streak}`;

    const avatar = document.getElementById("userAvatar");
    const menu = document.getElementById("profileMenu");
    avatar.addEventListener("click", () => {
        menu.style.display = menu.style.display === "none" ? "block" : "none";
        menu.innerHTML = `<div>Hello, ${name}</div>`;
    });
    document.body.addEventListener("click", e => {
        if (!avatar.contains(e.target) && !menu.contains(e.target)) {
            menu.style.display = "none";
        }
    });
});

// === FILTERS ===
document.getElementById("searchInput").addEventListener("input", performSearch);
document.getElementById("countryFilter").addEventListener("change", fetchContacts);
document.getElementById("specialFilter").addEventListener("change", fetchContacts);

// === INIT ===
document.addEventListener("DOMContentLoaded", fetchUserLimits);
document.addEventListener("DOMContentLoaded", fetchContacts);