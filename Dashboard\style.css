/* Base Layout */
body {
  margin: 0;
  font-family: 'Inter', 'Roboto', system-ui, sans-serif;
  background: linear-gradient(180deg, #e6edff 0%, #f5f8ff 100%);
  color: #1a1a1a;
  overflow-x: hidden;
  overflow-y: hidden

}

/* Container */
.dashboard-container {
  display: flex;
  min-height: 100vh;
  width: 100%;
}

/* Sidebar */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #00b4ff 0%, #007bff 100%);
  color: #fff;
  padding: 32px 24px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  flex-shrink: 0;
}

/* Sidebar Title */
.sidebar h2 {
  background: rgb(255, 255, 255);
  color: #000000;
  font-size: 1.3rem;
  font-weight: 700;
  padding: 16px 24px;
  border-radius: 16px;
  margin-bottom: 40px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
}

/* Sidebar Menu */
.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
}

.sidebar-menu li {
  margin-bottom: 16px;
}

.sidebar-menu a {
  display: flex;
  align-items: center;
  padding: 14px 20px;
  font-weight: 600;
  font-size: 1.05rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.sidebar-menu a::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: #fff;
  transform: scaleY(0);
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
  background: rgba(255, 255, 255, 0.95);
  color: #007bff;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.sidebar-menu a:hover::before,
.sidebar-menu a.active::before {
  transform: scaleY(1);
}

/* Sidebar User Info */
.sidebar-user {
  margin-top: auto;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  padding: 20px;
  border-radius: 16px;
  color: #fff;
  font-size: 0.95rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 60px 48px;
  background: linear-gradient(180deg, #f0f4ff 0%, #e6edff 100%);
  min-height: 100vh;
  box-sizing: border-box;
}

.main-content h1 {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 48px;
  color: #1e3a8a;
  position: relative;
  display: inline-block;
}

.main-content h1::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60%;
  height: 4px;
  background: linear-gradient(90deg, #00b4ff, #007bff);
  border-radius: 2px;
}

/* Profile Section */
.profile-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  margin-bottom: 64px;
}

.profile-box {
  background: rgba(255, 255, 255, 0.85);
  padding: 32px 24px;
  border-radius: 24px;
  text-align: center;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.profile-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.2), transparent 70%);
  opacity: 0;
}

.profile-box:hover::before {
  opacity: 0.3; /* Subtle overlay effect on hover */
}

.profile-box p {
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: 12px;
  text-transform: uppercase;
  color: #1e3a8a;
  letter-spacing: 1.2px;
}

.profile-box span {
  font-size: 2.6rem;
  font-weight: 900;
  color: #1a1a1a;
}

/* Colored Boxes */
.green-box {
  background: linear-gradient(135deg, #22c55e 0%, #86efac 100%);
  color: #fff;
}

.orange-box {
  background: linear-gradient(135deg, #f97316 0%, #fdba74 100%);
  color: #fff;
}

.blue-box {
  background: linear-gradient(135deg, #06b6d4 0%, #67e8f9 100%);
  color: #fff;
}

.green-box:hover {
  background: linear-gradient(135deg, #4ade80 0%, #a7f3d0 100%);
}

.orange-box:hover {
  background: linear-gradient(135deg, #fb923c 0%, #fed7aa 100%);
}

.blue-box:hover {
  background: linear-gradient(135deg, #22d3ee 0%, #a5f3fc 100%);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    width: 240px;
  }

  .main-content {
    padding: 50px 40px;
  }

  .main-content h1 {
    font-size: 2.2rem;
  }

  .profile-section {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
    padding: 20px;
    box-shadow: none;
  }

  .sidebar h2 {
    font-size: 1.2rem;
    padding: 12px 20px;
    margin-bottom: 24px;
  }

  .sidebar-menu a {
    padding: 12px 16px;
    font-size: 1rem;
  }

  .main-content {
    padding: 24px 20px;
  }

  .main-content h1 {
    font-size: 1.8rem;
    margin-bottom: 32px;
  }

  .profile-section {
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 40px;
  }

  .profile-box {
    padding: 24px 20px;
  }

  .profile-box span {
    font-size: 2.2rem;
  }
}

@media (max-width: 480px) {
  .sidebar {
    padding: 16px;
  }

  .sidebar h2 {
    font-size: 1.1rem;
    padding: 10px 16px;
    margin-bottom: 20px;
  }

  .sidebar-menu a {
    padding: 10px 12px;
    font-size: 0.95rem;
  }

  .main-content {
    padding: 20px 15px;
  }

  .main-content h1 {
    font-size: 1.6rem;
    margin-bottom: 24px;
  }

  .profile-section {
    gap: 16px;
    margin-bottom: 32px;
  }

  .profile-box {
    padding: 20px 16px;
    border-radius: 16px;
  }

  .profile-box p {
    font-size: 0.9rem;
    margin-bottom: 8px;
  }

  .profile-box span {
    font-size: 2rem;
  }
}

@media (max-width: 360px) {
  .main-content h1 {
    font-size: 1.4rem;
  }

  .profile-box {
    padding: 16px 12px;
  }

  .profile-box p {
    font-size: 0.85rem;
  }

  .profile-box span {
    font-size: 1.8rem;
  }
}

/* Footer Styles */
footer {
  background: linear-gradient(90deg, #00b4ff, #007bff);
  color: #fff;
  text-align: center;
  padding: 2rem;
  font-size: 0.9rem;
  border-top: 4px solid #ffd700;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
}

footer .footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

footer p {
  margin: 0;
  line-height: 1.5;
}

footer .footer-links {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
}

footer a {
  color: #fff;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease, transform 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

footer a:hover {
  color: #ffd700;
  transform: translateY(-2px);
}

footer a i {
  font-size: 1rem;
}

/* Footer responsive styles */
@media (max-width: 768px) {
  footer {
    padding: 1.5rem 1rem;
    font-size: 0.85rem;
  }

  footer .footer-links {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  footer {
    padding: 1.2rem 0.8rem;
    font-size: 0.8rem;
  }

  footer .footer-links {
    gap: 0.8rem;
  }

  footer a {
    font-size: 0.8rem;
  }
}

.top-right-logo {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 50px; /* Adjust size as needed */
  height: auto;
  z-index: 1001; /* Ensure it's above other content */
}
