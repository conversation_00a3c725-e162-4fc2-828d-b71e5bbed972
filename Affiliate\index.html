<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>LeadBaseAI | Affiliate</title>
  <link rel="stylesheet" href="style.css" />
  
  <link rel="apple-touch-icon" sizes="180x180" href="../icons/180-180.png">
  <link rel="icon" type="image/png" sizes="32x32" href="../icons/32-32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="../icons/16-16.png">
  <link rel="icon" type="image/png" sizes="192x192" href="../icons/192-192.png">
</head>
<body>
  <div class="sidebar">
    <h2>Menu</h2>
    <ul class="sidebar-menu">
      <li><a href="../Dashboard/index.html">Dashboard</a></li>
      <li><a href="../Data/index.html">Data</a></li>
      <li><a href="../Affiliate/index.html" class="active">Affiliate</a></li>
      <li><a href="#" id="logoutLink">Logout</a></li>
    </ul>
  </div>

  <div class="main-content">
    <div id="affiliate-container" class="active">
      <h2 style="margin-bottom: 24px;">AFFILIATE DASHBOARD</h2>
      <div class="affiliate-box">
        <p class="affiliate-label">Your unique affiliate link:</p>
        <input type="text" id="refLink" class="affiliate-input" readonly />
        <button id="generateBtn" class="affiliate-btn">Generate My Affiliate Link</button>
      </div>
    </div>
  </div>

  <script type="module">
    import { getUserLimits, clearAllData } from "../utils/app.js";

    document.getElementById("generateBtn").addEventListener("click", async () => {
      const userData = await getUserLimits();
      if (!userData?.email || !userData?.ip) {
        alert("Please log in again to access your affiliate link.");
        window.location.href = "../index.html";
        return;
      }

      const encoded = encodeURIComponent(userData.email);
      const link = `https://leadbaseai.in/login/index.html?affiliate=${encoded}`;
      document.getElementById("refLink").value = link;
    });

    document.getElementById("logoutLink").addEventListener("click", async (e) => {
      e.preventDefault();
      await clearAllData();
      window.location.href = "../index.html";
    });
  </script>
</body>
</html>
