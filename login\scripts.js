// ===== ELEMENTS =====
const loadingOverlay = document.getElementById('loadingOverlay');
const form = document.getElementById('loginForm');
const otpForm = document.getElementById('otpForm');

// ===== HELPERS =====
function showLoading() {
    loadingOverlay?.classList.add('visible');
}
function hideLoading() {
    loadingOverlay?.classList.remove('visible');
}
function hideError() {
    const errorBox = document.getElementById('error-message');
    errorBox.style.display = 'none';
}

// ===== ERROR DISPLAY =====
function showError(msg) {
    const errorBox = document.getElementById('error-message');
    errorBox.textContent = msg;
    errorBox.style.display = 'block';
    setTimeout(() => {
        errorBox.style.display = 'none';
    }, 5000);
}

// ===== SAVE WRAPPER =====
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        const verify = JSON.parse(localStorage.getItem(key) || '{}');
        console.log(`✅ Saved to localStorage: ${key}`, verify);
        return true;
    } catch (e) {
        console.error(`❌ Failed to save to localStorage: ${e}`);
        showError("Could not save data. Try enabling browser storage.");
        return false;
    }
}

// ===== LOCAL STORAGE TEST =====
try {
    localStorage.setItem("__test_key__", "ok");
    localStorage.removeItem("__test_key__");
    console.log("✅ localStorage is available.");
} catch (e) {
    console.error("❌ localStorage is blocked!", e);
}

// ===== COOKIE AUTO-LOGIN CHECK =====
document.addEventListener("DOMContentLoaded", async () => {
    console.log("🍪 Checking cookie/session...");
    try {
        const response = await fetch("lg.php", {
            method: "POST",
            headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
            body: JSON.stringify({})
        });

        const result = await response.json();
        console.log("🔁 Cookie Check Response:", result);

        if (result.status === "verified" && result.redirect) {
            let meta = {};

            if (result.user_data) {
                meta.name = result.user_data.name || "Anonymous";
                meta.email = result.user_data.email || "";
                meta.phone = result.user_data.phone || "";
            }

            const today = new Date().toISOString().split("T")[0];
            meta.streak = meta.streak ?? 0;
            meta.last_login = meta.last_login || today;
            console.log(meta);

            if (saveToLocalStorage("user_meta", meta)) {
                setTimeout(() => {
                    console.log("➡️ Redirecting:", result.redirect);
                    window.location.href = result.redirect;
                }, 300);
            }
        }
    } catch (err) {
        console.warn("Cookie check failed:", err);
    }
});

// ===== LOGIN SUBMIT HANDLER =====
// ... rest of your existing code ...

// ===== LOGIN SUBMIT HANDLER =====
form?.addEventListener("submit", async (e) => {
    e.preventDefault();
    showLoading();
    hideError();

    const email = document.getElementById('email')?.value.trim();
    const name = document.getElementById('name')?.value.trim();
    const phone = document.getElementById('phone')?.value.trim();

    if (!email || !name || !phone) {
        showError("All fields are required.");
        hideLoading();
        return;
    }

    if (!/^[a-zA-Z\s]{2,50}$/.test(name)) {
        showError("Name must be 2–50 letters.");
        hideLoading();
        return;
    }

    if (!/^\+?\d{10,15}$/.test(phone)) {
        showError("Invalid phone format.");
        hideLoading();
        return;
    }

    if (!email.endsWith('@gmail.com')) {
        showError("Only Gmail addresses allowed.");
        hideLoading();
        return;
    }

    const meta = { name, email, phone };

    try {
        const response = await fetch("lg.php", {
            method: "POST",
            headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
            body: JSON.stringify({ email, name, phone })
        });

        const result = await response.json();
        console.log("📥 Login result:", result);

        if (result.status === "otp_sent") {
            // ✅ Save to localStorage *unconditionally* when OTP is sent
            saveToLocalStorage("user_meta", meta);

            showError("OTP sent to your email.");
            form.style.display = "none";
            otpForm.style.display = "block";
        } else if (result.redirect) {
            setTimeout(() => {
                console.log("➡️ Redirecting:", result.redirect);
                window.location.href = result.redirect;
            }, 300);
        } else {
            showError(result.error || "Unknown error.");
        }
    } catch (err) {
        console.error("Login request failed:", err);
        showError("Server error. Try again.");
    } finally {
        hideLoading();
    }
});

// ===== OTP FORM HANDLER =====
otpForm?.addEventListener("submit", async (e) => {
    e.preventDefault();
    showLoading();
    hideError();

    const otp = document.getElementById('otp')?.value.trim();

    if (!otp || otp.length !== 6 || !/^\d{6}$/.test(otp)) {
        showError("Enter a valid 6-digit OTP.");
        hideLoading();
        return;
    }

    try {
        const response = await fetch("lg.php", {
            method: "POST",
            headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
            body: JSON.stringify({ otp })
        });

        const result = await response.json();
        console.log("📥 OTP verify result:", result);

        if (result.status === "verified" && result.redirect) {
            let meta = {};

            if (result.user_data) {
                meta.name = result.user_data.name || "Anonymous";
                meta.email = result.user_data.email || "";
                meta.phone = result.user_data.phone || "";
            }

            const today = new Date().toISOString().split("T")[0];
            meta.streak = meta.streak ?? 0;
            meta.last_login = meta.last_login || today;

            if (saveToLocalStorage("user_data", meta)) {
                setTimeout(() => {
                    console.log("➡️ Redirecting:", result.redirect);
                    window.location.href = result.redirect;
                }, 300);
            }
        } else {
            showError(result.error || "Invalid OTP.");
        }
    } catch (err) {
        console.error("OTP request failed:", err);
        showError("Server error. Try again.");
    } finally {
        hideLoading();
    }
});

// ===== DEBUG INFO ON LOAD =====
window.addEventListener('load', () => {
    console.log("🟢 Page fully loaded.");
    console.log("LocalStorage user_meta:", localStorage.getItem("user_meta"));
    console.log("Login form:", form);
    console.log("OTP form:", otpForm);
    console.log("Cookies:", document.cookie);
});