// ===== COOKIE AUTO-LOGIN =====
document.addEventListener("DOMContentLoaded", async () => {
    console.log("🍪 Checking cookie/session...");
    try {
        const response = await fetch("lg.php", {
            method: "POST",
            headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
            body: JSON.stringify({})
        });

        const result = await response.json();
        console.log("🔁 Cookie Check Response:", result);

        if (result.status === "verified" && result.redirect) {
            let meta = {};

            if (result.user_data) {
                meta.name = result.user_data.name || "Anonymous";
                meta.email = result.user_data.email || "";
                meta.phone = result.user_data.phone || "";
            }

            const today = new Date().toISOString().split("T")[0];
            meta.streak = meta.streak ?? 0;
            meta.last_login = meta.last_login || today;
            console.log(meta);

            if (saveToLocalStorage("user_meta", meta.name)) {
                setTimeout(() => {
                    console.log("➡️ Redirecting:", result.redirect);
                    window.open(result.redirect,"_blank");;
                }, 300);
            }
        }
    } catch (err) {
        console.warn("Cookie check failed:", err);
    }
});

// ===== SAVE WRAPPER =====
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        const verify = JSON.parse(localStorage.getItem(key) || '{}');
        console.log(`✅ Saved to localStorage: ${key}`, verify);
        return true;
    } catch (e) {
        console.error(`❌ Failed to save to localStorage: ${e}`);
        showError("Could not save data. Try enabling browser storage.");
        return false;
    }
}

// ===== HELPERS =====
function showError(msg) {
    const errorBox = document.getElementById('error-message');
    errorBox.textContent = msg;
    errorBox.style.display = 'block';
    setTimeout(() => {
        errorBox.style.display = 'none';
    }, 5000);
}