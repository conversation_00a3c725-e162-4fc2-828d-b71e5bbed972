<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LeadFlow - Professional Contact Management</title>
    <link rel="stylesheet" href="styles.css">

    <link rel="apple-touch-icon" sizes="180x180" href="../icons/180-180.png">
    <link rel="icon" type="image/png" sizes="32x32" href="../icons/32-32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="../icons/16-16.png">

</head>

<body>
    <!-- Modern Header -->
    <header class="modern-header">
        <!-- Logo Section -->
        <div class="logo-section">
            <div class="logo-container">
                <img src="../icons/32-32.png" alt="LeadBaseAI Logo" class="logo-image">
            </div>
            <div class="brand-info">
                <h1 class="brand-title">LeadBaseAI</h1>
                <p class="brand-subtitle">AI-Powered Contact Intelligence</p>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="main-nav">
            <a href="#" class="nav-link active">
                <span class="nav-icon">📊</span>
                Dashboard
            </a>
            <a href="#" class="nav-link">
                <span class="nav-icon">🗃️</span>
                Database
            </a>
            <a href="#" class="nav-link">
                <span class="nav-icon">🛠️</span>
                Tools
            </a>
            <a href="#" class="nav-link">
                <span class="nav-icon">📈</span>
                Reports
            </a>
            <a href="#" class="nav-link">
                <span class="nav-icon">💬</span>
                Support
            </a>
        </nav>

        <!-- User Section -->
        <div class="user-section">
            <!-- Limits Display in Header -->
            <div class="header-limits">
                <div class="header-limit-item">
                    <span class="header-limit-label">Daily</span>
                    <span class="header-limit-value" id="dailyLimit">...</span>
                </div>
                <div class="header-limit-item">
                    <span class="header-limit-label">Extra</span>
                    <span class="header-limit-value" id="extraLimit">...</span>
                </div>
            </div>
            <div id="streakBox" class="streak-badge">
                <span class="streak-icon">🔥</span>
                <span class="streak-text">Streak: 0</span>
            </div>
            <div class="user-menu">
                <div id="userAvatar" class="user-avatar">
                    <span class="avatar-text">--</span>
                    <div class="avatar-status"></div>
                </div>
                <div class="profile-menu" id="profileMenu">
                    <!-- Dropdown by JS -->
                </div>
            </div>
        </div>
    </header>
<style>
    /* Additional animations and enhancements */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Page load animations */
    .modern-header {
        animation: slideInLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .container {
        animation: fadeInUp 1s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
    }

    .controls {
        animation: fadeInUp 1s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
    }

    .table-container {
        animation: fadeInUp 1s cubic-bezier(0.4, 0, 0.2, 1) 0.6s both;
    }

    /* Floating elements */
    .floating-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: -1;
        overflow: hidden;
    }

    .floating-bg::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.03) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.03) 0%, transparent 50%);
        animation: float 20s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translate(0, 0) rotate(0deg); }
        33% { transform: translate(30px, -30px) rotate(120deg); }
        66% { transform: translate(-20px, 20px) rotate(240deg); }
    }
</style>



    <!-- Floating Background -->
    <div class="floating-bg"></div>

    <!-- Main Container -->
    <div class="container">
        <!-- Breadcrumb -->
        <div class="breadcrumb">
            <a href="#">Dashboard</a> > Database
        </div>

        <!-- Page Header -->
        <div class="page-header">
            <h2 class="page-title">Database</h2>
            <p class="page-subtitle">Manage and organize your professional contacts</p>
        </div>

        <!-- Controls -->
        <div class="controls">
            <div class="controls-row">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="Search contacts..." id="searchInput">
                    <svg class="search-icon" width="16" height="16" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                </div>
                <div class="filter-group">
                    <select class="filter-select" id="countryFilter">
                        <option value="" disabled selected>All Countries</option>
                        <option value="usa" selected>USA</option>
                        <option value="canada">Canada</option>
                        <option value="uk">United Kingdom</option>
                        <option value="india">India</option>
                        <option value="australia">Australia</option>
                        <option value="uae">UAE</option>
                    </select>
                    <select class="filter-select" id="specialFilter">
                        <option value="" disabled selected>Advance Filters</option>
                        <option value="none">None</option>
                        <option value="have_email">Have Email</option>
                        <option value="have_phone">Have Phone</option>
                        <option value="have_both">Have Both</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Action Bar -->
        <div class="action-bar" id="actionBar">
            <div class="selected-info">
                <span id="selectedCount">0</span> contacts selected
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" id="downloadBtn">Download</button>
                <button class="btn btn-secondary" id="saveBtn">Save</button>
            </div>
        </div>

        <!-- Table Container -->
        <div class="table-container">
            <div class="table-header">
                <h3 class="table-title">Available Data</h3>
                <div class="results-info">Total: <span id="totalResults">1,280</span> contacts</div>
            </div>

            <table id="contactTable">
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <input type="checkbox" class="checkbox" id="selectAll">
                        </th>
                        <th class="sortable">
                            Name
                            <span class="sort-icon">↕</span>
                        </th>
                        <th class="sortable">
                            Phone
                            <span class="sort-icon">↕</span>
                        </th>
                        <th class="sortable">
                            Email
                            <span class="sort-icon">↕</span>
                        </th>
                        <th>Bio</th>
                        <th>Facebook</th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="pagination">
                <div class="pagination-info" id="paginationInfo">
                    Showing 1-10 of 1,280 contacts
                </div>
                <div class="pagination-controls">
                    <button class="page-btn" id="prevBtn" disabled>← Prev</button>
                    <span id="pageNumbers"></span>
                    <button class="page-btn" id="nextBtn">Next →</button>
                </div>
            </div>
        </div>
    </div>
    <script src="scripts.js"></script>
</body>

</html>