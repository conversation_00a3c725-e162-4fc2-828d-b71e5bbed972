<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LeadFlow - Professional Contact Management</title>
    <link rel="stylesheet" href="styles.css">

    <link rel="apple-touch-icon" sizes="180x180" href="../icons/180-180.png">
    <link rel="icon" type="image/png" sizes="32x32" href="../icons/32-32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="../icons/16-16.png">

</head>

<body>
    <!-- Header -->
    <header
        style="display:flex;align-items:center;justify-content:space-between;padding:16px 32px;background:#e6f3ff;border-bottom:2px solid #cce6ff;box-shadow:0 2px 6px rgba(0,0,0,0.05);font-family:'Segoe UI', sans-serif;animation:fadeIn 0.8s ease;">
        <!-- Logo Section -->
        <div style="display:flex;align-items:center;gap:14px;">
            <img src="../icons/32-32.png" alt="LeadBaseAI Logo" style="height:36px;width:36px;">
            <div>
                <h1 style="margin:0;font-size:22px;font-weight:700;color:#0059b3;letter-spacing:0.5px;">LeadBaseAI</h1>
                <p style="margin:0;font-size:12px;color:#3399ff;">AI-Powered Contact Intelligence</p>
            </div>
        </div>

        <!-- Navigation -->
        <nav style="display:flex;align-items:center;gap:28px;">
            <a href="#" style="color:#0059b3;text-decoration:none;font-weight:500;transition:color 0.3s ease;"
                onmouseover="this.style.color='#0077e6'" onmouseout="this.style.color='#0059b3'">Dashboard</a>
            <a href="#" style="color:#0059b3;text-decoration:none;font-weight:500;transition:color 0.3s ease;"
                onmouseover="this.style.color='#0077e6'" onmouseout="this.style.color='#0059b3'">Database</a>
            <a href="#" style="color:#0059b3;text-decoration:none;font-weight:500;transition:color 0.3s ease;"
                onmouseover="this.style.color='#0077e6'" onmouseout="this.style.color='#0059b3'">Tools</a>
            <a href="#" style="color:#0059b3;text-decoration:none;font-weight:500;transition:color 0.3s ease;"
                onmouseover="this.style.color='#0077e6'" onmouseout="this.style.color='#0059b3'">Reports</a>
            <a href="#" style="color:#0059b3;text-decoration:none;font-weight:500;transition:color 0.3s ease;"
                onmouseover="this.style.color='#0077e6'" onmouseout="this.style.color='#0059b3'">Support</a>
        </nav>

        <!-- User Section -->
        <div style="display:flex;align-items:center;gap:16px;">
            <div id="streakBox"
                style="background:#4caf50;padding:6px 14px;border-radius:20px;font-size:13px;font-weight:600;color:white;box-shadow:0 2px 4px rgba(0,0,0,0.1);transition:all 0.3s ease;">
                🔥 Streak: 0
            </div>
            <div style="position:relative;">
                <div id="userAvatar"
                    style="background:#0077cc;color:white;border-radius:50%;width:36px;height:36px;display:flex;align-items:center;justify-content:center;font-weight:700;font-size:15px;cursor:pointer;transition:transform 0.3s ease;"
                    onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                    --
                </div>
                <div class="profile-menu" id="profileMenu"
                    style="display:none;position:absolute;top:44px;right:0;background:#ffffff;border:1px solid #ccc;padding:10px;border-radius:6px;z-index:100;box-shadow:0 2px 8px rgba(0,0,0,0.08);">
                    <!-- Dropdown by JS -->
                </div>
            </div>
        </div>
    </header>
<style>
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>



    <!-- Main Container -->
    <div class="container">
        <!-- Breadcrumb -->
        <div class="breadcrumb">
            <a href="#">Dashboard</a> > Contact Database
        </div>
        <div style="margin:10px 0;font-size:14px;">
            Daily Limit: <span id="dailyLimit">...</span> | Extra Limit: <span id="extraLimit">...</span>
        </div>


        <!-- Page Header -->
        <div class="page-header">
            <h2 class="page-title">Contact Database</h2>
            <p class="page-subtitle">Manage and organize your professional contacts</p>
        </div>

        <!-- Controls -->
        <div class="controls">
            <div class="controls-row">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="Search contacts..." id="searchInput">
                    <svg class="search-icon" width="16" height="16" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                </div>
                <div class="filter-group">
                    <select class="filter-select" id="countryFilter">
                        <option value="" disabled selected>All Countries</option>
                        <option value="usa" selected>USA</option>
                        <option value="canada">Canada</option>
                        <option value="uk">United Kingdom</option>
                        <option value="india">India</option>
                        <option value="australia">Australia</option>
                        <option value="uae">UAE</option>
                    </select>
                    <select class="filter-select" id="specialFilter">
                        <option value="" disabled selected>Advance Filters</option>
                        <option value="none">None</option>
                        <option value="have_email">Have Email</option>
                        <option value="have_phone">Have Phone</option>
                        <option value="have_both">Have Both</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Action Bar -->
        <div class="action-bar" id="actionBar">
            <div class="selected-info">
                <span id="selectedCount">0</span> contacts selected
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" id="downloadBtn">Download</button>
                <button class="btn btn-secondary" id="saveBtn">Save</button>
            </div>
        </div>

        <!-- Table Container -->
        <div class="table-container">
            <div class="table-header">
                <h3 class="table-title">Available Data</h3>
                <div class="results-info">Total: <span id="totalResults">1,280</span> contacts</div>
            </div>

            <table id="contactTable">
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <input type="checkbox" class="checkbox" id="selectAll">
                        </th>
                        <th class="sortable">
                            Name
                            <span class="sort-icon">↕</span>
                        </th>
                        <th class="sortable">
                            Phone
                            <span class="sort-icon">↕</span>
                        </th>
                        <th class="sortable">
                            Email
                            <span class="sort-icon">↕</span>
                        </th>
                        <th>Bio</th>
                        <th>Facebook</th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="pagination">
                <div class="pagination-info" id="paginationInfo">
                    Showing 1-10 of 1,280 contacts
                </div>
                <div class="pagination-controls">
                    <button class="page-btn" id="prevBtn" disabled>← Prev</button>
                    <span id="pageNumbers"></span>
                    <button class="page-btn" id="nextBtn">Next →</button>
                </div>
            </div>
        </div>
    </div>
    <script src="scripts.js"></script>
</body>

</html>