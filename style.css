* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Poppins', sans-serif;
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 50%, #f0f6ff 100%);
  overflow-x: hidden;
  position: relative;
  line-height: 1.6;
  color: #2c3e50;
}

/* Animated Background Particles */
.bg-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  background: rgba(0, 123, 255, 0.1);
  border-radius: 50%;
  animation: float 20s infinite linear;
}

.particle:nth-child(1) {
  width: 10px;
  height: 10px;
  left: 10%;
  animation-delay: 0s;
}

.particle:nth-child(2) {
  width: 15px;
  height: 15px;
  left: 20%;
  animation-delay: 2s;
}

.particle:nth-child(3) {
  width: 8px;
  height: 8px;
  left: 30%;
  animation-delay: 4s;
}

.particle:nth-child(4) {
  width: 12px;
  height: 12px;
  left: 40%;
  animation-delay: 6s;
}

.particle:nth-child(5) {
  width: 20px;
  height: 20px;
  left: 50%;
  animation-delay: 8s;
}

.particle:nth-child(6) {
  width: 6px;
  height: 6px;
  left: 60%;
  animation-delay: 10s;
}

.particle:nth-child(7) {
  width: 18px;
  height: 18px;
  left: 70%;
  animation-delay: 12s;
}

.particle:nth-child(8) {
  width: 14px;
  height: 14px;
  left: 80%;
  animation-delay: 14s;
}

.particle:nth-child(9) {
  width: 10px;
  height: 10px;
  left: 90%;
  animation-delay: 16s;
}

header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 50%, #00c6ff 100%);
  color: white;
  padding: 0;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.15);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.2rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 3s infinite;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo-section h1 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-section h1 i {
  color: #ffd700;
  font-size: 1.6rem;
}

.tagline {
  font-size: 0.75rem;
  opacity: 0.9;
  font-weight: 400;
  margin-top: -2px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Hamburger and Navigation */
.hamburger {
  display: none;
  font-size: 1.8rem;
  color: white;
  cursor: pointer;
  z-index: 1002;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  min-width: 44px;
  min-height: 44px;
  align-items: center;
  justify-content: center;
}

.hamburger:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-menu {
  display: flex;
  gap: 1.5rem;
}

.nav-menu a {
  color: white;
  text-decoration: none;
  font-weight: 500;
  padding: 10px 18px;
  border-radius: 25px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
}

.nav-menu a i {
  font-size: 0.9rem;
  opacity: 0.8;
}

.nav-cta {
  background: linear-gradient(45deg, #ffd700, #ffed4e) !important;
  color: #000 !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.nav-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.nav-menu a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgb(255, 255, 255);
  transition: left 0.3s ease;
  z-index: -1;
}

.nav-menu a:hover::before {
  left: 0;
}

.nav-menu a:hover {
  color: #000000;
  transform: translateY(-2px);
}

/* Mobile Navigation */
@media (max-width: 768px) {
  header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    text-align: left;
    padding: 1rem 1.5rem;
    position: relative;
  }

  .hamburger {
    display: flex;
    margin-left: auto;
    font-size: 1.5rem;
    transition: transform 0.3s ease;
  }

  .hamburger:hover {
    transform: scale(1.1);
  }

  #nav-menu {
    display: none;
    flex-direction: column;
    background: linear-gradient(90deg, #007bff, #00c6ff);
    width: 100%;
    position: absolute;
    top: 100%;
    left: 0;
    padding: 1rem 2rem;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 0 0 12px 12px;
  }

  #nav-menu.show {
    display: flex;
    animation: slideDown 0.3s ease-out;
  }

  #nav-menu a {
    padding: 12px 0;
    font-size: 1rem;
    width: 100%;
    text-align: center;
    border-radius: 8px;
    margin: 2px 0;
    transition: all 0.3s ease;
  }

  #nav-menu a:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
  }
}

/* Desktop Navigation */
@media (min-width: 769px) {
  #nav-menu {
    display: flex;
    flex-direction: row;
    background: transparent;
    padding: 0;
    margin-top: 0;
  }

  .hamburger {
    display: none;
  }
}

.hero {
  background: linear-gradient(135deg, #f8faff 0%, #e6f3ff 50%, #ffffff 100%);
  color: #2c3e50;
  padding: 80px 20px 120px;
  position: relative;
  overflow: hidden;
}



.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  position: relative;
  z-index: 1;
}

.hero-content {
  text-align: left;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(0, 123, 255, 0.2);
}

.hero h2 {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
}

.highlight {
  background: linear-gradient(135deg, #007bff, #00c6ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #64748b;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 2.5rem;
}

.stat {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: #007bff;
  line-height: 1;
}

.stat-label {
  font-size: 0.85rem;
  color: #64748b;
  margin-top: 0.25rem;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
}

.cta-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  padding: 16px 32px;
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
}

.cta-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 123, 255, 0.4);
}

.cta-secondary {
  background: transparent;
  color: #007bff;
  font-weight: 500;
  font-size: 1rem;
  padding: 16px 24px;
  border: 2px solid #007bff;
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.cta-secondary:hover {
  background: #007bff;
  color: white;
  transform: translateY(-2px); 
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
  transition: all 0.3s ease;
}

.hero .cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: buttonShimmer 3s infinite;
}

.hero .cta:hover {
  transform: scale(1.1) translateY(-3px);
  box-shadow: 0 15px 35px rgba(255, 215, 0, 0.6);
}

.hero-features {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #64748b;
}

.feature-item i {
  color: #22c55e;
  font-size: 0.8rem;
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.dashboard-preview {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 123, 255, 0.15);
  overflow: hidden;
  width: 100%;
  max-width: 500px;
}

.preview-header {
  background: linear-gradient(135deg, #f8faff 0%, #e6f3ff 100%);
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.preview-dots {
  display: flex;
  gap: 0.5rem;
}

.preview-dots span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #cbd5e1;
}

.preview-dots span:nth-child(1) { background: #ef4444; }
.preview-dots span:nth-child(2) { background: #f59e0b; }
.preview-dots span:nth-child(3) { background: #22c55e; }

.preview-title {
  font-weight: 600;
  color: #475569;
  font-size: 0.9rem;
}

.preview-content {
  padding: 2rem 1.5rem;
}

.preview-chart {
  display: flex;
  align-items: end;
  gap: 0.5rem;
  height: 120px;
  margin-bottom: 1.5rem;
}

.chart-bar {
  background: linear-gradient(135deg, #007bff 0%, #00c6ff 100%);
  border-radius: 4px 4px 0 0;
  flex: 1;
}

.preview-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.metric {
  text-align: center;
  padding: 1rem;
  background: #f8faff;
  border-radius: 12px;
}

.metric-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #007bff;
}

.metric-label {
  font-size: 0.8rem;
  color: #64748b;
  margin-top: 0.25rem;
}

.features {
  padding: 100px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  position: relative;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h3 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.section-header p {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2.5rem 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 123, 255, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 123, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #007bff 0%, #00c6ff 100%);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px rgba(0, 123, 255, 0.15);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #007bff 0%, #00c6ff 100%);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.feature-icon i {
  font-size: 1.5rem;
  color: white;
}

.feature-card h4 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.feature-card p {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.feature-benefits {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.feature-benefits span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #22c55e;
  font-weight: 500;
}

.feature-benefits i {
  font-size: 0.8rem;
}

.how-it-works {
  padding: 100px 20px;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
}

.steps-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 0 1rem;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 200px;
  max-width: 200px;
  position: relative;
  flex-shrink: 0;
}

.step-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #007bff 0%, #00c6ff 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.step-content {
  text-align: center;
}

.step-icon {
  width: 35px;
  height: 35px;
  background: rgba(0, 123, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.5rem;
}

.step-icon i {
  font-size: 1rem;
  color: #007bff;
}

.step h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.step p {
  color: #64748b;
  line-height: 1.3;
  font-size: 0.8rem;
}

.step-connector {
  width: 40px;
  height: 2px;
  background: linear-gradient(135deg, #007bff 0%, #00c6ff 100%);
  position: relative;
  flex-shrink: 0;
}

.step-connector::after {
  content: '';
  position: absolute;
  right: -4px;
  top: -2px;
  width: 0;
  height: 0;
  border-left: 6px solid #00c6ff;
  border-top: 3px solid transparent;
  border-bottom: 3px solid transparent;
}

.testimonials {
  padding: 100px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.testimonial-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 123, 255, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 123, 255, 0.1);
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 50px rgba(0, 123, 255, 0.15);
}

.testimonial-content {
  margin-bottom: 1.5rem;
}

.stars {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.stars i {
  color: #ffd700;
  font-size: 1rem;
}

.testimonial-content p {
  color: #2c3e50;
  line-height: 1.6;
  font-style: italic;
  font-size: 1rem;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #007bff 0%, #00c6ff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.author-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.author-title {
  font-size: 0.9rem;
  color: #64748b;
}

.features h3:hover,
.how-it-works h3:hover {
  color: #000000;
}

.features .grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: auto;
  padding: 20px;
}

.card {
  background: linear-gradient(135deg, #eaf4ff 0%, #ddeeff 100%);
  padding: 30px 20px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  animation: cardFloat 1s ease-out;
  border: 2px solid transparent;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 123, 255, 0.1), rgba(0, 198, 255, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover::before {
  opacity: 1;
}

.card:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 20px 40px rgba(0, 123, 255, 0.2);
  border-color: #007bff;
}

.card h4 {
  color: #0047b3;
  margin-bottom: 1rem;
  font-size: 1.3rem;
  animation: slideInLeft 1s ease-out;
  transition: color 0.3s ease;
}

.card:hover h4 {
  color: #000000;
}

.card p {
  animation: slideInRight 1s ease-out 0.2s both;
  line-height: 1.6;
  color: #333333;
  transition: color 0.3s ease;
}

.card:hover p {
  color: #000000;
}

.how-it-works ol {
  list-style: none;
  max-width: 800px;
  margin: auto;
  counter-reset: step-counter;
}

.how-it-works li {
  counter-increment: step-counter;
  position: relative;
  padding: 20px 0 20px 80px;
  margin: 20px 0;
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(0, 198, 255, 0.05) 100%);
  border-radius: 10px;
  animation: slideInAlternate 1s ease-out;
  transition: all 0.3s ease;
  color: #333333;
}

.how-it-works li:hover {
  transform: translateX(10px);
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%);
  color: #000000;
}

.how-it-works li::before {
  content: counter(step-counter);
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #007bff, #00c6ff);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;

}

.cta-section {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 50%, #00c6ff 100%);
  color: white;
  padding: 100px 20px;
  position: relative;
  overflow: hidden;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 3rem;
  align-items: center;
  text-align: left;
}

.cta-text h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.cta-text p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.cta-benefits {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.benefit {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.benefit i {
  color: #22c55e;
  font-size: 0.9rem;
}

.cta-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.cta-primary.large {
  font-size: 1.2rem;
  padding: 20px 40px;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #000;
  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.4);
}

.cta-primary.large:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(255, 215, 0, 0.5);
}

.cta-note {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

.cta-note i {
  color: #22c55e;
}



.cta-section h3 {
  animation: zoomIn 1s ease-out;
  margin-bottom: 1rem;
  transition: color 0.3s ease;
}

.cta-section h3:hover {
  color: #000000;
}

.cta-section p {
  animation: fadeIn 1.5s ease-out 0.5s both;
  margin-bottom: 2rem;
  transition: color 0.3s ease;
}

.cta-section p:hover {
  color: #000000;
}

.cta-section a.cta {
  background: linear-gradient(45deg, white, #f0f8ff);
  color: #007bff;
  padding: 16px 40px;
  font-size: 1.3rem;
  font-weight: bold;
  border-radius: 50px;
  text-decoration: none;
  display: inline-block;
  position: relative;
  overflow: hidden;

  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.4s ease;
}

.cta-section a.cta::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  border-radius: 50%;
  transition: all 0.4s ease;
  transform: translate(-50%, -50%);
}

.cta-section a.cta:hover::before {
  width: 300px;
  height: 300px;
}

.cta-section a.cta:hover {
  color: black;
  transform: scale(1.1) translateY(-5px);
  box-shadow: 0 15px 40px rgba(255, 215, 0, 0.4);
}

.cta-section a.cta span {
  position: relative;
  z-index: 1;
}

.modern-footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 4rem 2rem 2rem;
  font-size: 0.95rem;
  position: relative;
  overflow: hidden;
  border-top: 4px solid #007bff;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-brand h4 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-brand h4 i {
  color: #007bff;
}

.footer-brand p {
  color: #bdc3c7;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-links a {
  width: 40px;
  height: 40px;
  background: rgba(0, 123, 255, 0.1);
  border: 1px solid rgba(0, 123, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #007bff;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: #007bff;
  color: white;
  transform: translateY(-3px);
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.footer-column h5 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #007bff;
}

.footer-column a {
  display: block;
  color: #bdc3c7;
  text-decoration: none;
  padding: 0.5rem 0;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-column a:hover {
  color: white;
  transform: translateX(5px);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
}

.footer-legal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.legal-links {
  display: flex;
  gap: 2rem;
}

.legal-links a {
  color: #bdc3c7;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.legal-links a:hover {
  color: white;
}

.modern-footer .footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.modern-footer p {
  margin: 0;
  line-height: 1.5;
}

.modern-footer .footer-links {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
}

.modern-footer a {
  color: white;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease, transform 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modern-footer a:hover {
  color: #ffd700;
  transform: translateY(-3px);
}

.modern-footer a i {
  font-size: 1.1rem;
}

.modern-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
  animation: gradientShift 3s ease-in-out infinite;
}

/* Keyframe Animations */
@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes glow {
  0% {
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  }
  100% {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 30px rgba(255, 255, 255, 0.6);
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideInBounce {
  0% {
    transform: translateY(-100px);
    opacity: 0;
  }
  60% {
    transform: translateY(10px);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
  }
  50% {
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.6);
  }
}

@keyframes buttonShimmer {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeInScale {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes cardFloat {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInAlternate {
  from {
    transform: translateX(-30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}



@keyframes backgroundMove {
  0% {
    transform: translateX(0) translateY(0);
  }
  100% {
    transform: translateX(50px) translateY(50px);
  }
}

@keyframes zoomIn {
  from {
    transform: scale(0.5);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}



@keyframes scaleIn {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  header {
    flex-direction: column;
    text-align: center;
  }

  .nav-menu {
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 1rem;
    gap: 1rem;
  }

  .hero h2 {
    font-size: 2rem;
  }

  .hero p {
    font-size: 1rem;
  }

  .hero .cta {
    font-size: 1rem;
    padding: 12px 24px;
  }

  .cta-section h3 {
    font-size: 1.5rem;
  }

  .cta-section p {
    font-size: 1rem;
  }

  .cta-section a.cta {
    font-size: 1rem;
    padding: 12px 24px;
  }
}

@media (max-width: 768px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .hero-content {
    text-align: center;
  }

  .hero h2 {
    font-size: 2.5rem;
  }

  .hero-stats {
    justify-content: center;
  }

  .hero-actions {
    justify-content: center;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .steps-container {
    flex-wrap: wrap;
    gap: 1.5rem;
    justify-content: center;
  }

  .step {
    min-width: 150px;
    max-width: 150px;
  }

  .step-connector {
    display: none;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
  }

  .cta-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .footer-main {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .footer-legal {
    flex-direction: column;
    text-align: center;
  }

  .features .grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 10px;
  }

  .how-it-works ol {
    padding: 0 10px;
  }

  .how-it-works li {
    padding-left: 50px;
    font-size: 0.95rem;
    margin: 15px 0;
  }

  .how-it-works li::before {
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
  }

  .hero {
    padding: 60px 15px;
  }

  .hero h2 {
    font-size: 1.8rem;
    line-height: 1.3;
  }

  .hero p {
    font-size: 1rem;
    margin: 1rem 0 1.5rem;
  }

  .cta-section {
    padding: 40px 15px;
  }

  .cta-section h3 {
    font-size: 1.5rem;
  }

  .cta-section p {
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
  }

  .modern-footer {
    font-size: 0.85rem;
    padding: 2rem 1rem;
  }

  .modern-footer .footer-content {
    gap: 1.5rem;
  }

  .footer-links {
    flex-direction: column;
    gap: 1.2rem;
  }

  .modern-footer a {
    justify-content: center;
    padding: 8px;
  }
}

@media (max-width: 600px) {
  header {
    padding: 1rem 1rem;
  }

  header h1 {
    font-size: 1.6rem;
  }

  .nav-menu {
    gap: 0.5rem;
  }

  .nav-menu a {
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  .hero {
    padding: 80px 15px;
  }

  .hero h2 {
    font-size: 2.2rem;
  }

  .hero p {
    font-size: 1.1rem;
  }

  .hero .cta {
    font-size: 1.1rem;
    padding: 12px 28px;
  }

  .features,
  .how-it-works,
  .cta-section {
    padding: 40px 15px;
  }

  .features h3,
  .how-it-works h3 {
    font-size: 1.8rem;
  }

  .features .grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .card {
    padding: 25px 15px;
  }

  .card h4 {
    font-size: 1.2rem;
  }

  .how-it-works li {
    padding: 15px 0 15px 60px;
    font-size: 0.9rem;
  }

  .how-it-works li::before {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
    left: 15px;
  }

  .cta-section h3 {
    font-size: 1.6rem;
  }

  .cta-section p {
    font-size: 0.95rem;
  }

  .cta-section a.cta {
    font-size: 1.1rem;
    padding: 14px 32px;
  }

  .modern-footer {
    padding: 2rem 1.5rem;
    font-size: 0.9rem;
  }

  .modern-footer .footer-links {
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  header {
    padding: 0.8rem 1rem;
  }

  header h1 {
    font-size: 1.4rem;
  }

  .hero {
    padding: 50px 10px;
  }

  .hero h2 {
    font-size: 1.8rem;
    line-height: 1.2;
  }

  .hero p {
    font-size: 0.95rem;
    margin: 1rem 0 1.5rem;
  }

  .hero .cta {
    font-size: 1rem;
    padding: 12px 24px;
  }

  .features,
  .how-it-works,
  .cta-section {
    padding: 30px 10px;
  }

  .features h3,
  .how-it-works h3 {
    font-size: 1.6rem;
  }

  .cta-section h3 {
    font-size: 1.4rem;
  }

  .cta-section p {
    font-size: 0.9rem;
  }

  .cta-section a.cta {
    font-size: 1rem;
    padding: 12px 24px;
  }

  .how-it-works li {
    padding: 12px 0 12px 50px;
    font-size: 0.9rem;
  }

  .how-it-works li::before {
    width: 30px;
    height: 30px;
    font-size: 0.85rem;
    left: 12px;
  }

  .nav-menu a {
    font-size: 0.9rem;
    padding: 8px 12px;
  }

  .card {
    padding: 20px 15px;
  }

  .card h4 {
    font-size: 1.15rem;
  }

  .card p {
    font-size: 0.9rem;
  }

  .modern-footer {
    padding: 1.5rem 1rem;
    font-size: 0.8rem;
  }

  .modern-footer .footer-links {
    gap: 0.8rem;
  }

  .modern-footer a {
    font-size: 0.8rem;
  }
}

/* Extra small devices (320px and below) */
@media (max-width: 360px) {
  header h1 {
    font-size: 1.2rem;
  }

  .hero h2 {
    font-size: 1.5rem;
  }

  .hero p {
    font-size: 0.85rem;
  }

  .hero .cta {
    font-size: 0.9rem;
    padding: 10px 20px;
  }

  .features h3,
  .how-it-works h3 {
    font-size: 1.4rem;
  }

  .cta-section h3 {
    font-size: 1.2rem;
  }

  .cta-section p {
    font-size: 0.8rem;
  }

  .cta-section a.cta {
    font-size: 0.9rem;
    padding: 10px 20px;
  }

  .card h4 {
    font-size: 1rem;
  }

  .card p {
    font-size: 0.85rem;
  }

  .how-it-works li {
    padding-left: 45px;
    font-size: 0.85rem;
  }

  .how-it-works li::before {
    width: 25px;
    height: 25px;
    font-size: 0.75rem;
    left: 10px;
  }
}

/* Additional Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes growUp {
  0% { height: 0; }
  100% { height: var(--height, 100%); }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInBounce {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  60% {
    opacity: 1;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hide scrollbar but keep functionality */
html {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

html::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

/* Touch-friendly improvements for mobile */
@media (max-width: 768px) {
  /* Ensure touch targets are at least 44px */
  .nav-menu a,
  .cta,
  .card,
  button {
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Improve touch scrolling */
  body {
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent zoom on input focus */
  input, select, textarea {
    font-size: 16px;
  }

  /* Better tap highlighting */
  * {
    -webkit-tap-highlight-color: rgba(0, 123, 255, 0.2);
  }
}