/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* CSS Variables for LeadBaseAI Design System */
:root {
  /* Primary Colors - LeadBaseAI Blue Theme */
  --primary-50: #e6f3ff;
  --primary-100: #cce6ff;
  --primary-200: #99ccff;
  --primary-300: #66b3ff;
  --primary-400: #3399ff;
  --primary-500: #0080ff;
  --primary-600: #0066cc;
  --primary-700: #004d99;
  --primary-800: #003366;
  --primary-900: #001a33;

  /* Neutral Colors */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Accent Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #06b6d4;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #0080ff 0%, #0066cc 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;

  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: var(--gray-50);
  color: var(--gray-800);
  line-height: 1.6;
  font-size: 14px;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius-md);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Main Container */
.container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
  min-height: 100vh;
}

/* Breadcrumb */
.breadcrumb {
  margin-bottom: 2rem;
  color: var(--gray-500);
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.breadcrumb a {
  color: var(--primary-600);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
}

.breadcrumb a:hover {
  color: var(--primary-700);
}

/* Page Header */
.page-header {
  margin-bottom: 3rem;
  text-align: center;
  position: relative;
}

.page-header::before {
  content: '';
  position: absolute;
  top: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: var(--radius-sm);
}

.page-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--gray-900);
  margin-bottom: 0.75rem;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, var(--gray-900) 0%, var(--primary-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  color: var(--gray-600);
  font-size: 1.125rem;
  font-weight: 400;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.7;
}

/* Controls Section */
.controls {
  background: white;
  border-radius: var(--radius-xl);
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  position: relative;
  overflow: hidden;
}

.controls-row {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  flex-wrap: wrap;
}

/* Search Box */
.search-box {
  flex: 1;
  min-width: 320px;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 1rem 1.25rem 1rem 3rem;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  font-size: 15px;
  font-weight: 500;
  transition: all var(--transition-normal);
  background: var(--gray-50);
  color: var(--gray-800);
}

.search-input::placeholder {
  color: var(--gray-400);
  font-weight: 400;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-500);
  background: white;
  box-shadow: 0 0 0 3px rgba(0, 128, 255, 0.1);
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  transition: color var(--transition-fast);
}

.search-input:focus + .search-icon {
  color: var(--primary-500);
}

/* Filter Group */
.filter-group {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.filter-select {
  padding: 1rem 1.25rem;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  font-size: 14px;
  font-weight: 500;
  background: var(--gray-50);
  min-width: 160px;
  transition: all var(--transition-normal);
  color: var(--gray-700);
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-500);
  background: white;
  box-shadow: 0 0 0 3px rgba(0, 128, 255, 0.1);
}

.filter-select:hover {
  border-color: var(--gray-300);
  background: white;
}

/* Action Bar */
.action-bar {
  background: var(--primary-100);
  color: var(--gray-800);
  padding: 1.25rem 2rem;
  border-radius: var(--radius-xl);
  margin-bottom: 1.5rem;
  display: none;
  align-items: center;
  justify-content: space-between;
  animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--primary-200);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 50;
}

.action-bar.show {
  display: flex;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.selected-info {
  font-weight: 600;
  font-size: 15px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--gray-800);
}

.selected-info::before {
  content: '✓';
  background: var(--primary-500);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
}

.action-buttons {
  display: flex;
  gap: 1rem;
}

/* Modern Button Styles */
.btn {
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: none;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background: var(--gray-600);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
  background: var(--gray-700);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn-secondary:active {
  transform: translateY(0);
}

/* Table Container */
.table-container {
  background: white;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--gray-200);
  position: relative;
}

.table-header {
  padding: 2rem;
  border-bottom: 2px solid var(--gray-100);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
}

.table-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-900);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.table-title::before {
  content: '📊';
  font-size: 1.25rem;
}

.results-info {
  color: var(--gray-600);
  font-size: 15px;
  font-weight: 500;
  background: var(--primary-50);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--primary-200);
}

/* Table Styles */
table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

thead {
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-50) 100%);
  position: sticky;
  top: 0;
  z-index: 10;
}

th {
  text-align: left;
  padding: 1.25rem 1.5rem;
  font-weight: 700;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: var(--gray-700);
  border-bottom: 2px solid var(--gray-200);
  border-right: 1px solid var(--gray-200);
  cursor: pointer;
  position: relative;
  transition: all var(--transition-normal);
  background: transparent;
}

th:last-child {
  border-right: none;
}

th:hover {
  background: var(--gray-200);
  color: var(--gray-900);
}

th:first-child {
  border-top-left-radius: var(--radius-lg);
}

th:last-child {
  border-top-right-radius: var(--radius-lg);
}

.sort-icon {
  margin-left: 0.5rem;
  opacity: 0.4;
  transition: all var(--transition-fast);
  font-size: 14px;
}

th.sortable:hover .sort-icon {
  opacity: 1;
  transform: scale(1.1);
}

/* Table Body */
tbody tr {
  border-bottom: 1px solid var(--gray-100);
  transition: background-color 0.2s ease;
  position: relative;
}

tbody tr:nth-child(even) {
  background: var(--gray-50);
}

tbody tr:hover {
  background: var(--primary-50);
}

tbody tr.selected {
  background: var(--primary-100);
  border-color: var(--primary-300);
}

td {
  padding: 1.25rem 1.5rem;
  font-size: 14px;
  vertical-align: middle;
  font-weight: 500;
  border-right: 1px solid var(--gray-200);
}

td:last-child {
  border-right: none;
}

/* Modern Checkbox */
.checkbox {
  width: 18px;
  height: 18px;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  appearance: none;
  background: white;
}

.checkbox:hover {
  border-color: var(--primary-400);
}

.checkbox:checked {
  background: var(--primary-500);
  border-color: var(--primary-500);
}

.checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: 700;
}

/* Contact Information Styling */
.contact-name {
  font-weight: 600;
  color: var(--gray-900);
  font-size: 15px;
}

.contact-phone {
  color: var(--gray-600);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 13px;
  background: var(--gray-100);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  display: inline-block;
}

.contact-email {
  color: var(--primary-600);
  font-weight: 500;
  text-decoration: none;
  transition: color var(--transition-fast);
}

.contact-email:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

.bio-text {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--gray-600);
  font-size: 13px;
  line-height: 1.5;
}

.facebook-link {
  color: var(--primary-600);
  text-decoration: none;
  font-weight: 600;
  transition: all var(--transition-fast);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  background: var(--primary-50);
  border: 1px solid var(--primary-200);
  display: inline-block;
  font-size: 12px;
}

.facebook-link:hover {
  color: white;
  background: var(--primary-600);
  border-color: var(--primary-600);
  transform: translateY(-1px);
}

/* Pagination */
.pagination {
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid var(--gray-200);
  background: white;
}

.pagination-info {
  color: var(--gray-600);
  font-size: 14px;
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.page-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--gray-300);
  background: white;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  color: var(--gray-700);
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-btn:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.page-btn.active {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-btn:disabled:hover {
  background: white;
  border-color: var(--gray-300);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 1.5rem 1rem;
  }

  .page-title {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem 0.75rem;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .controls {
    padding: 1.5rem;
  }

  .controls-row {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .search-box {
    min-width: unset;
  }

  .filter-group {
    justify-content: stretch;
    flex-wrap: wrap;
  }

  .filter-select {
    flex: 1;
    min-width: 140px;
  }

  .action-bar {
    padding: 1rem 1.5rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .action-buttons {
    justify-content: center;
  }

  .table-header {
    padding: 1.5rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .pagination {
    padding: 1.5rem;
    flex-direction: column;
    gap: 1rem;
  }

  .pagination-controls {
    justify-content: center;
  }
}

/* Enhanced Table Styling */
#contactTable {
  font-size: 14px;
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  background: white;
  border: 1px solid var(--gray-200);
}

#contactTable th,
#contactTable td {
  border-bottom: 1px solid var(--gray-200);
  border-right: 1px solid var(--gray-200);
  padding: 1.25rem 1.5rem;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#contactTable th:last-child,
#contactTable td:last-child {
  border-right: none;
}

#contactTable th {
  background: var(--gray-50);
  font-weight: 700;
  position: sticky;
  top: 0;
  z-index: 10;
  color: var(--gray-700);
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  border-bottom: 2px solid var(--gray-200);
}

#contactTable tr:nth-child(even) {
  background: var(--gray-50);
}

#contactTable tr.selected {
  background: var(--primary-100);
}

#contactTable tr:hover {
  background: var(--primary-50);
  cursor: pointer;
}

.table-container {
  overflow-x: auto;
  max-width: 100%;
  border-radius: var(--radius-2xl);
}

/* Responsive Column Widths */
#contactTable th:nth-child(1) { width: 5%; }
#contactTable th:nth-child(2) { width: 22%; }
#contactTable th:nth-child(3) { width: 16%; }
#contactTable th:nth-child(4) { width: 18%; }
#contactTable th:nth-child(5) { width: 32%; }
#contactTable th:nth-child(6) { width: 7%; }

/* Mobile Responsive */
@media (max-width: 768px) {
  #contactTable {
    font-size: 12px;
  }

  #contactTable th,
  #contactTable td {
    padding: 0.75rem 1rem;
  }

  #contactTable th:nth-child(1) { width: 8%; }
  #contactTable th:nth-child(2) { width: 25%; }
  #contactTable th:nth-child(3) { width: 20%; }
  #contactTable th:nth-child(4) { width: 25%; }
  #contactTable th:nth-child(5) { width: 20%; }
  #contactTable th:nth-child(6) { width: 12%; }
}

@media (max-width: 480px) {
  #contactTable {
    font-size: 11px;
  }

  #contactTable th,
  #contactTable td {
    padding: 0.5rem 0.75rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .controls {
    padding: 1rem;
  }

  .table-header {
    padding: 1rem;
  }

  .pagination {
    padding: 1rem;
  }
}

/* Loading Animation */
@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.loading-shimmer {
  background: linear-gradient(90deg, var(--gray-100) 25%, var(--gray-200) 50%, var(--gray-100) 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Modern Header Styles */
.modern-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: var(--gradient-primary);
  border-bottom: none;
  box-shadow: 0 2px 10px rgba(0, 128, 255, 0.2);
  font-family: 'Inter', sans-serif;
  position: relative;
  overflow: hidden;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  z-index: 2;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-image {
  height: 32px;
  width: 32px;
  border-radius: 6px;
}

.logo-glow {
  display: none;
}

.brand-info {
  color: white;
}

.brand-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 800;
  color: white;
  letter-spacing: -0.025em;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.brand-subtitle {
  margin: 0;
  font-size: 0.875rem;
  color: rgba(255,255,255,0.9);
  font-weight: 500;
}

/* Navigation */
.main-nav {
  display: flex;
  align-items: center;
  gap: 2rem;
  z-index: 2;
}

.nav-link {
  color: rgba(255,255,255,0.9);
  text-decoration: none;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all var(--transition-normal);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link:hover {
  color: white;
  background: rgba(255,255,255,0.1);
  transform: translateY(-1px);
}

.nav-link.active {
  color: white;
  background: rgba(255,255,255,0.15);
  box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.nav-icon {
  font-size: 1rem;
}

/* User Section */
.user-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  z-index: 2;
}

/* Header Limits */
.header-limits {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.header-limit-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 70px;
}

.header-limit-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.header-limit-value {
  font-size: 1rem;
  font-weight: 700;
  color: white;
  margin-top: 0.25rem;
}

.streak-badge {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-2xl);
  font-size: 0.875rem;
  font-weight: 700;
  color: white;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.streak-badge:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.streak-icon {
  font-size: 1rem;
  animation: flicker 2s infinite;
}

@keyframes flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.user-menu {
  position: relative;
}

.user-avatar {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  box-shadow: var(--shadow-md);
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.avatar-text {
  z-index: 2;
  position: relative;
}

.avatar-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #10b981;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: var(--shadow-sm);
}

.profile-menu {
  display: none;
  position: absolute;
  top: 52px;
  right: 0;
  background: white;
  border: 1px solid var(--gray-200);
  padding: 0.75rem;
  border-radius: var(--radius-lg);
  z-index: 100;
  box-shadow: var(--shadow-xl);
  min-width: 200px;
}



/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.text-sm { font-size: 0.875rem; }
.text-xs { font-size: 0.75rem; }
