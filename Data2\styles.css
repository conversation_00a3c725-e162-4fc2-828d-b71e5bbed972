        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: #f8fafc;
          color: #1e293b;
          line-height: 1.6;
        }

        /* Header */
        .header {
          background: white;
          border-bottom: 1px solid #e2e8f0;
          padding: 1rem 2rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .logo-section {
          display: flex;
          align-items: center;
          gap: 0.75rem;
        }

        .logo {
          width: 32px;
          height: 32px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 700;
          font-size: 16px;
        }

        .brand-info h1 {
          font-size: 20px;
          font-weight: 700;
          color: #1e293b;
        }

        .brand-info p {
          font-size: 13px;
          color: #64748b;
          margin: 0;
        }

        .user-section {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .user-avatar {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 600;
          font-size: 14px;
        }

        /* Main Container */
        .container {
          max-width: 1400px;
          margin: 0 auto;
          padding: 2rem;
        }

        .breadcrumb {
          margin-bottom: 2rem;
          color: #64748b;
          font-size: 14px;
        }

        .breadcrumb a {
          color: #3b82f6;
          text-decoration: none;
        }

        .page-header {
          margin-bottom: 2rem;
        }

        .page-title {
          font-size: 28px;
          font-weight: 700;
          color: #1e293b;
          margin-bottom: 0.5rem;
        }

        .page-subtitle {
          color: #64748b;
          font-size: 16px;
        }

        /* Controls Section */
        .controls {
          background: white;
          border-radius: 12px;
          padding: 1.5rem;
          margin-bottom: 1.5rem;
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
          border: 1px solid #e2e8f0;
        }

        .controls-row {
          display: flex;
          gap: 1rem;
          align-items: center;
          flex-wrap: wrap;
        }

        .search-box {
          flex: 1;
          min-width: 300px;
          position: relative;
        }

        .search-input {
          width: 100%;
          padding: 0.75rem 1rem 0.75rem 2.5rem;
          border: 2px solid #e2e8f0;
          border-radius: 8px;
          font-size: 14px;
          transition: all 0.2s;
          background: #f8fafc;
        }

        .search-input:focus {
          outline: none;
          border-color: #3b82f6;
          background: white;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-icon {
          position: absolute;
          left: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          color: #64748b;
        }

        .filter-group {
          display: flex;
          gap: 0.75rem;
        }

        .filter-select {
          padding: 0.75rem 1rem;
          border: 2px solid #e2e8f0;
          border-radius: 8px;
          font-size: 14px;
          background: #f8fafc;
          min-width: 140px;
          transition: all 0.2s;
        }

        .filter-select:focus {
          outline: none;
          border-color: #3b82f6;
          background: white;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Action Bar */
        .action-bar {
          background: #1e293b;
          color: white;
          padding: 1rem 1.5rem;
          border-radius: 8px;
          margin-bottom: 1rem;
          display: none;
          align-items: center;
          justify-content: space-between;
          animation: slideDown 0.3s ease-out;
        }

        .action-bar.show {
          display: flex;
        }

        @keyframes slideDown {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }

          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .selected-info {
          font-weight: 500;
        }

        .action-buttons {
          display: flex;
          gap: 0.75rem;
        }

        .btn {
          padding: 0.5rem 1rem;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          border: none;
        }

        .btn-primary {
          background: #3b82f6;
          color: white;
        }

        .btn-primary:hover {
          background: #2563eb;
          transform: translateY(-1px);
        }

        .btn-secondary {
          background: #64748b;
          color: white;
        }

        .btn-secondary:hover {
          background: #475569;
          transform: translateY(-1px);
        }

        /* Table Container */
        .table-container {
          background: white;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
          border: 1px solid #e2e8f0;
        }

        .table-header {
          padding: 1.5rem;
          border-bottom: 1px solid #e2e8f0;
          display: flex;
          justify-content: between;
          align-items: center;
        }

        .table-title {
          font-size: 18px;
          font-weight: 600;
          color: #1e293b;
        }

        .results-info {
          color: #64748b;
          font-size: 14px;
        }

        table {
          width: 100%;
          border-collapse: collapse;
        }

        thead {
          background: #f8fafc;
        }

        th {
          text-align: left;
          padding: 1rem 1.5rem;
          font-weight: 600;
          font-size: 12px;
          text-transform: uppercase;
          letter-spacing: 0.05em;
          color: #475569;
          border-bottom: 1px solid #e2e8f0;
          cursor: pointer;
          position: relative;
          transition: background-color 0.2s;
        }

        th:hover {
          background: #e2e8f0;
        }

        .sort-icon {
          margin-left: 0.5rem;
          opacity: 0.3;
        }

        th.sortable:hover .sort-icon {
          opacity: 1;
        }

        tbody tr {
          border-bottom: 1px solid #f1f5f9;
          transition: all 0.2s;
        }

        tbody tr:nth-child(even) {
          background: #fafbfc;
        }

        tbody tr:hover {
          background: #f0f9ff;
          transform: scale(1.001);
        }

        tbody tr.selected {
          background: #dbeafe;
        }

        td {
          padding: 1rem 1.5rem;
          font-size: 14px;
          vertical-align: middle;
        }

        .checkbox {
          width: 16px;
          height: 16px;
          border: 2px solid #d1d5db;
          border-radius: 3px;
          cursor: pointer;
          transition: all 0.2s;
        }

        .checkbox:checked {
          background: #3b82f6;
          border-color: #3b82f6;
        }

        .contact-name {
          font-weight: 500;
          color: #1e293b;
        }

        .contact-phone {
          color: #64748b;
          font-family: 'SF Mono', Monaco, monospace;
        }

        .contact-email {
          color: #64748b;
        }

        .bio-text {
          max-width: 300px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #64748b;
        }

        .facebook-link {
          color: #3b82f6;
          text-decoration: none;
          font-weight: 500;
          transition: color 0.2s;
        }

        .facebook-link:hover {
          color: #2563eb;
          text-decoration: underline;
        }

        /* Pagination */
        .pagination {
          padding: 1.5rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-top: 1px solid #e2e8f0;
          background: #f8fafc;
        }

        .pagination-info {
          color: #64748b;
          font-size: 14px;
        }

        .pagination-controls {
          display: flex;
          gap: 0.5rem;
          align-items: center;
        }

        .page-btn {
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          background: white;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          transition: all 0.2s;
        }

        .page-btn:hover {
          background: #f3f4f6;
          border-color: #9ca3af;
        }

        .page-btn.active {
          background: #3b82f6;
          color: white;
          border-color: #3b82f6;
        }

        .page-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        /* Responsive */
        @media (max-width: 768px) {
          .container {
            padding: 1rem;
          }

          .controls-row {
            flex-direction: column;
            align-items: stretch;
          }

          .search-box {
            min-width: unset;
          }

          .filter-group {
            justify-content: stretch;
          }

          .filter-select {
            flex: 1;
          }
        }

/* Table styling for visibility and responsiveness */
#contactTable {
  font-size: 11px;
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

#contactTable th,
#contactTable td {
  border: 1px solid #d0d0d0;
  padding: 6px;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#contactTable th {
  background-color: #f0f0f0;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 1;
}

#contactTable tr:nth-child(even) {
  background-color: #f8f8f8;
}

#contactTable tr.selected {
  background-color: #e3f2fd;
}

#contactTable tr:hover {
  background-color: #e8e8e8;
  cursor: pointer;
}

.table-container {
  overflow-x: auto;
  max-width: 100%;
}

/* Responsive column widths */
#contactTable th:nth-child(1) {
  width: 5%;
}

#contactTable th:nth-child(2) {
  width: 20%;
}

#contactTable th:nth-child(3) {
  width: 14%;
}

#contactTable th:nth-child(4) {
  width: 14%;
}

#contactTable th:nth-child(5) {
  width: 40%;
}

#contactTable th:nth-child(6) {
  width: 7%;
}

@media (max-width: 768px) {
  #contactTable {
      font-size: 10px;
  }

  #contactTable th,
  #contactTable td {
      padding: 4px;
  }

  #contactTable th:nth-child(1) {
      width: 8%;
  }

  #contactTable th:nth-child(2) {
      width: 25%;
  }

  #contactTable th:nth-child(3) {
      width: 20%;
  }

  #contactTable th:nth-child(4) {
      width: 25%;
  }

  #contactTable th:nth-child(5) {
      width: 20%;
  }

  #contactTable th:nth-child(6) {
      width: 12%;
  }
}

@media (max-width: 480px) {
  #contactTable {
      font-size: 9px;
  }

  #contactTable th,
  #contactTable td {
      padding: 3px;
  }
}
